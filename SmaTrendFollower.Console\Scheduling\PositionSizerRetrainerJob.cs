using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Quartz;
using SmaTrendFollower.MachineLearning.DataPrep;
using SmaTrendFollower.MachineLearning.ModelTraining;
using SmaTrendFollower.Services;
using SmaTrendFollower.Monitoring;
using StackExchange.Redis;
using System.Diagnostics;

namespace SmaTrendFollower.Scheduling;

/// <summary>
/// Quartz.NET job for automated weekly position sizer model retraining.
/// Runs 30 minutes after the signal ranker retraining to ensure fresh data.
/// </summary>
[DisallowConcurrentExecution]
public class PositionSizerRetrainerJob : IJob
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<PositionSizerRetrainerJob> _logger;

    public PositionSizerRetrainerJob(IServiceProvider serviceProvider, ILogger<PositionSizerRetrainerJob> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        _logger.LogInformation("🎯 Starting position sizer model retraining job...");
        
        var startTime = DateTime.UtcNow;
        var success = false;
        string? errorMessage = null;

        // Start metrics tracking
        var stopwatch = Stopwatch.StartNew();

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var featureExportService = scope.ServiceProvider.GetRequiredService<IFeatureExportService>();
            var positionSizerService = scope.ServiceProvider.GetService<PositionSizerService>();
            var redisService = scope.ServiceProvider.GetService<OptimizedRedisConnectionService>();

            // Step 1: Export position sizing features
            var csvPath = "Model/positions.csv";
            var modelPath = "Model/position_model.zip";
            var backupPath = $"Model/Backups/position_model_{DateTime.UtcNow:yyyyMMdd_HHmmss}.zip";

            _logger.LogInformation("📊 Exporting position sizing features...");
            
            // Export last 24 months of data
            var fromDate = DateTime.UtcNow.AddMonths(-24);
            var toDate = DateTime.UtcNow;
            
            await featureExportService.ExportPositionSizingCsvAsync(csvPath, fromDate, toDate);

            // Validate exported data
            if (!File.Exists(csvPath))
            {
                throw new InvalidOperationException("Position sizing CSV export failed - file not created");
            }

            var csvLines = await File.ReadAllLinesAsync(csvPath);
            if (csvLines.Length < 2) // Header + at least 1 data row
            {
                throw new InvalidOperationException("Position sizing CSV export failed - no data exported");
            }

            _logger.LogInformation("✅ Exported {Count} position sizing records", csvLines.Length - 1);

            // Step 2: Backup existing model if it exists
            if (File.Exists(modelPath))
            {
                var backupDirectory = Path.GetDirectoryName(backupPath);
                if (!string.IsNullOrEmpty(backupDirectory) && !Directory.Exists(backupDirectory))
                {
                    Directory.CreateDirectory(backupDirectory);
                }
                
                File.Copy(modelPath, backupPath, overwrite: true);
                _logger.LogInformation("📦 Backed up existing model to {BackupPath}", backupPath);
            }

            // Step 3: Train new model using TrainPositionSizer
            _logger.LogInformation("🤖 Training new position sizing model...");
            
            var trainingResult = await TrainPositionSizingModelAsync(csvPath, modelPath);
            
            _logger.LogInformation("✅ Position sizing model training completed successfully!");
            _logger.LogInformation("Model R²: {RSquared:F4}, MAE: {MAE:F6}, RMSE: {RMSE:F6}", 
                trainingResult.RSquared, trainingResult.MeanAbsoluteError, trainingResult.RootMeanSquaredError);

            // Step 4: Update Redis with new model version
            if (redisService != null)
            {
                try
                {
                    var database = await redisService.GetDatabaseAsync();
                    var modelVersion = File.GetLastWriteTimeUtc(modelPath).Ticks;
                    
                    await database.StringSetAsync("model:position:version", modelVersion, 
                        RedisKeyConstants.RedisKeyTTL.MLModel);
                    
                    // Store model metadata
                    var metadata = new
                    {
                        Version = modelVersion,
                        TrainedAt = DateTime.UtcNow,
                        ModelType = "LightGBM Position Sizer",
                        RSquared = trainingResult.RSquared,
                        MeanAbsoluteError = trainingResult.MeanAbsoluteError,
                        RootMeanSquaredError = trainingResult.RootMeanSquaredError,
                        TrainingSamples = trainingResult.TrainingSamples
                    };
                    
                    await database.StringSetAsync("model:position:metadata", 
                        System.Text.Json.JsonSerializer.Serialize(metadata),
                        RedisKeyConstants.RedisKeyTTL.MLModel);
                    
                    _logger.LogInformation("Updated Redis with new position model version: {Version}", modelVersion);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to update Redis with new position model version");
                }
            }

            // Step 5: Reload model in PositionSizerService
            if (positionSizerService != null)
            {
                await positionSizerService.ReloadModelAsync();
                _logger.LogInformation("Reloaded position sizing model in PositionSizerService");
            }

            // Step 6: Update Prometheus metrics
            MetricsRegistry.MLModelAccuracy.Set(trainingResult.RSquared);
            MetricsRegistry.MLModelVersion.Set(File.GetLastWriteTimeUtc(modelPath).Ticks);

            // Step 7: Cleanup old backup files (keep last 5)
            await CleanupOldBackupsAsync();

            // Step 8: Log completion metrics
            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("🎉 Position sizer retraining completed successfully in {Duration:mm\\:ss}", duration);

            success = true;
            MetricsRegistry.MLRetrainRuns.WithLabels("position_sizer_success").Inc();
            
            // Record training duration
            stopwatch.Stop();
            MetricsRegistry.MLRetrainDuration.Observe(stopwatch.Elapsed.TotalSeconds);
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
            _logger.LogError(ex, "❌ Position sizer model retraining failed");
            MetricsRegistry.MLRetrainRuns.WithLabels("position_sizer_failure").Inc();
            throw; // Re-throw to mark job as failed
        }
        finally
        {
            // Log job summary
            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("Position sizer retraining job summary: Success={Success}, Duration={Duration:mm\\:ss}, Error={Error}",
                success, duration, errorMessage ?? "None");
        }
    }

    /// <summary>
    /// Trains the position sizing model using the TrainPositionSizer class
    /// </summary>
    private async Task<PositionSizerTrainingResult> TrainPositionSizingModelAsync(string csvPath, string modelPath)
    {
        // For now, we'll invoke the training logic directly
        // In a production environment, you might want to run this in a separate process
        
        var trainingTask = Task.Run(async () =>
        {
            try
            {
                // Simulate the training process that would normally be done by TrainPositionSizer.Main
                // This is a simplified version - in practice you'd want to use the full TrainPositionSizer logic
                
                var mlContext = new Microsoft.ML.MLContext(seed: 42);
                
                // Load data
                var data = mlContext.Data.LoadFromTextFile<SmaTrendFollower.MachineLearning.DataPrep.PositionSizingRow>(
                    csvPath, hasHeader: true, separatorChar: ',');
                
                // Create pipeline
                var pipeline = mlContext.Transforms.Concatenate("Features", 
                        "RankProb", "ATR_Pct", "AvgSpreadPct")
                    .Append(mlContext.Regression.Trainers.LightGbm(
                        labelColumnName: "EquityPctRisk",
                        featureColumnName: "Features",
                        numberOfLeaves: 31,
                        minimumExampleCountPerLeaf: 20,
                        learningRate: 0.1,
                        numberOfIterations: 100));
                
                // Split data
                var trainTestSplit = mlContext.Data.TrainTestSplit(data, testFraction: 0.2, seed: 42);
                
                // Train model
                var model = pipeline.Fit(trainTestSplit.TrainSet);
                
                // Evaluate
                var predictions = model.Transform(trainTestSplit.TestSet);
                var metrics = mlContext.Regression.Evaluate(predictions, labelColumnName: "EquityPctRisk");
                
                // Save model
                mlContext.Model.Save(model, data.Schema, modelPath);
                
                // Count training samples
                var dataList = mlContext.Data.CreateEnumerable<SmaTrendFollower.MachineLearning.DataPrep.PositionSizingRow>(
                    data, reuseRowObject: false).ToList();
                
                return new PositionSizerTrainingResult(
                    metrics.RSquared,
                    metrics.MeanAbsoluteError,
                    metrics.RootMeanSquaredError,
                    dataList.Count
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during position sizer model training");
                throw;
            }
        });

        return await trainingTask;
    }

    /// <summary>
    /// Cleans up old model backup files, keeping only the most recent 5
    /// </summary>
    private async Task CleanupOldBackupsAsync()
    {
        try
        {
            var backupDirectory = "Model/Backups";
            if (!Directory.Exists(backupDirectory))
                return;

            var backupFiles = Directory.GetFiles(backupDirectory, "position_model_*.zip")
                .Select(f => new FileInfo(f))
                .OrderByDescending(f => f.CreationTime)
                .ToList();

            if (backupFiles.Count <= 5)
                return;

            var filesToDelete = backupFiles.Skip(5);
            foreach (var file in filesToDelete)
            {
                file.Delete();
                _logger.LogDebug("Deleted old position model backup: {FileName}", file.Name);
            }

            _logger.LogInformation("Cleaned up {Count} old position model backup files", filesToDelete.Count());
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cleanup old position model backup files");
        }
    }
}

/// <summary>
/// Result of position sizer model training
/// </summary>
public record PositionSizerTrainingResult(
    double RSquared,
    double MeanAbsoluteError,
    double RootMeanSquaredError,
    int TrainingSamples
);
